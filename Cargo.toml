[workspace]
resolver = "2"
members = [
    "dw-test-item",
    "parquet-provider",
    "kafka-provider",
    "mysql-provider",
    "redis-provider",
    "ck-provider",
    "common",
    "utils"
]

[profile.release]
debug = true

[workspace.dependencies]
log = '0.4'
tokio = { version = "1", features = ["full"] }
anyhow = "1.0"
thiserror = "2.0.12"
arrow = '53.1.0'
serde = { version = '1.0.215', features = ['derive'] }
serde_json = '1.0.133'
chrono = '=0.4.38'
futures = "0.3"
async-trait = "0.1"
env_logger = '0.11.5'
serde_arrow = { version = '0.13.0', features = ['arrow-53'] }
parquet = '53.1.0'
rust_decimal = { version = "1.34", features = ["serde-with-str", "serde-with-float"] }
fixnum = { version = "0.9.3", features = ["serde", "i32", "i64", "i128"] }
sqlx = { version = "0.8.6", features = ["runtime-tokio", "mysql", "macros", "chrono", "rust_decimal"] }
tracing = '0.1.41'
tracing-subscriber = '0.3.19'
bumpalo = "3.13"
color-eyre = "0.6.4"


[patch.crates-io]
hdrs = { git = "http://gitlab.guwave.com/rust/hdrs.git", branch = "main" }