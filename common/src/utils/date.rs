// 实现DateTime<Utc>到时间戳的转换

use chrono::{DateTime, Local, TimeZone, Utc};

const DATETIME_FORMAT: &str = "%Y-%m-%d %H:%M:%S";
const DATETIME_FORMAT_WITH_ZONE: &str = "%Y-%m-%d %H:%M:%S %z";
const YYYYMMDD: &str = "%Y%m%d";
const YYYYMMDDHH: &str = "%Y%m%d%H";

pub trait IntoDateTime<Tz: TimeZone> {
    fn into_local(self) -> DateTime<Tz>;
}
pub trait IntoDateTimeUtc {
    fn into_utc(self) -> DateTime<Utc>;
}

pub trait Format {
    fn into_format(self) -> String;
}

impl IntoDateTime<Local> for i64 {
    fn into_local(self) -> DateTime<Local> {
        DateTime::from_timestamp(self, 0).unwrap().with_timezone(&Local)
    }
}

impl IntoDateTimeUtc for i64 {
    fn into_utc(self) -> DateTime<Utc> {
        DateTime::from_timestamp(self, 0).unwrap().with_timezone(&Utc)
    }
}

impl Format for DateTime<Utc> {
    fn into_format(self) -> String {
        self.with_timezone(&Local).format(DATETIME_FORMAT).to_string()
    }
}

impl Format for DateTime<Local> {
    fn into_format(self) -> String {
        self.format(DATETIME_FORMAT).to_string()
    }
}

pub fn parse_datetime(datetime: &str) -> DateTime<Local> {
    DateTime::parse_from_str(format!("{} +08:00", datetime).as_str(), DATETIME_FORMAT_WITH_ZONE)
        .unwrap()
        .with_timezone(&Local)
}

pub fn parse_datetime_format(datetime: &str, format: &str) -> DateTime<Local> {
    DateTime::parse_from_str(datetime, format).unwrap().with_timezone(&Local)
}

pub fn get_day(datetime: DateTime<Utc>) -> String {
    datetime.with_timezone(&Local).format(YYYYMMDD).to_string()
}

pub fn get_day_hour(datetime: DateTime<Utc>) -> String {
    datetime.with_timezone(&Local).format(YYYYMMDDHH).to_string()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_datetime() {
        let datetime = "2024-05-10 16:00:00";
        let parsed = parse_datetime(datetime);
        assert_eq!(parsed.timestamp(), 1715328000);
    }

    #[test]
    fn test_parse_datetime_format() {
        let datetime = "2024-05-10 16:00:00 +08:00";
        let format = "%Y-%m-%d %H:%M:%S %z";
        let parsed = parse_datetime_format(datetime, format);
        assert_eq!(parsed.timestamp(), 1715328000);
    }

    #[test]
    fn test_into_local() {
        let timestamp = 1715328000;
        let datetime_local = timestamp.into_local();
        let datetime_utc = DateTime::from_timestamp(timestamp, 0).unwrap();
        println!("datetime_local: {:?}", datetime_local);
        println!("datetime_utc: {:?}", datetime_utc);
        assert_eq!(datetime_local, datetime_utc);
    }

    #[test]
    fn test_into_format_tz() {
        let datetime = DateTime::from_timestamp(1715328000, 0).unwrap().with_timezone(&Local);
        let string = datetime.into_format();
        println!("string: {:?}", string);
        assert_eq!(string, "2024-05-10 16:00:00");
    }

    #[test]
    fn test_into_format_utc() {
        let datetime = DateTime::from_timestamp(1715328000, 0).unwrap();
        let string = datetime.into_format();
        println!("string: {:?}", string);
        assert_eq!(string, "2024-05-10 16:00:00");
    }

    #[test]
    fn test_into_format_local() {
        let datetime = DateTime::from_timestamp(1715328000, 0).unwrap().with_timezone(&Local);
        let string = datetime.into_format();
        println!("string: {:?}", string);
        assert_eq!(string, "2024-05-10 16:00:00");
    }

    #[test]
    fn test_get_day() {
        let timestamp = 1715328000;
        let day = get_day(timestamp.into_utc());
        assert_eq!(day, "20240510");
    }

    #[test]
    fn test_get_day_hour() {
        let timestamp = 1715328000;
        let day_hour = get_day_hour(timestamp.into_utc());
        assert_eq!(day_hour, "2024051016");
    }

    #[test]
    fn test_timestamp() {
        let datetime = DateTime::from_timestamp(1753173136, 0).unwrap();
        let timestamp = datetime.timestamp();
        assert_eq!(timestamp, 1753173136);
    }
}
