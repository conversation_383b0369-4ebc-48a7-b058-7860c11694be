use fixnum::{typenum::U18, FixedPoint};
use rust_decimal::Decimal;

// Decimal(38, 18)
// P = 38 (精度)：这是数字的总位数（整数部分 + 小数部分）。根据 ClickHouse 的规则：
// 1 <= P <= 9: 使用 Int32
// 10 <= P <= 18: 使用 Int64
// 19 <= P <= 38: 使用 Int128
// 39 <= P <= 76: 使用 Int256
pub type Decimal38_18 = FixedPoint<i128, U18>;

pub trait IntoDecimal38_18 {
    fn into_decimal38_18(self) -> Decimal38_18;
}

impl IntoDecimal38_18 for f64 {
    fn into_decimal38_18(self) -> Decimal38_18 {
        let decimal = self.to_string().parse::<Decimal>().unwrap();
        decimal.into_decimal38_18()
    }
}

impl IntoDecimal38_18 for f32 {
    fn into_decimal38_18(self) -> Decimal38_18 {
        let decimal = self.to_string().parse::<Decimal>().unwrap();
        decimal.into_decimal38_18()
    }
}

impl IntoDecimal38_18 for Decimal {
    fn into_decimal38_18(self) -> Decimal38_18 {
        let decimal_scale = self.scale();
        let target_scale = 18u32;

        let scaled_mantissa = if decimal_scale <= target_scale {
            // 需要放大mantissa
            self.mantissa() * 10i128.pow(target_scale - decimal_scale)
        } else {
            // 需要缩小mantissa
            self.mantissa() / 10i128.pow(decimal_scale - target_scale)
        };
        Decimal38_18::from_bits(scaled_mantissa)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_into_decimal38_18() {
        let decimal = "333.12345678901234567890".parse::<Decimal>().unwrap();
        let decimal38_18 = decimal.into_decimal38_18();
        assert_eq!(decimal38_18.to_string(), "333.123456789012345678");
    }

    #[test]
    fn test_into_decimal38_18_f64() {
        let f64 = 333.12345678901234567890f32;
        let decimal38_18 = f64.into_decimal38_18();
        assert_eq!(decimal38_18.to_string(), "333.12344");
    }
}
