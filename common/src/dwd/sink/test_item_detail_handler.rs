use crate::dto::dwd::test_item_detail_row::TestItemDetailRow;
use crate::dwd::sink::SinkHandler;
use crate::model::constant::{CLUSTER_NAME, CLUSTER_TABLE, LOCAL_TABLE};

const TABLE_NAME: &str = "dwd_test_item_detail_{CLUSTER}";
const PARTITION_EXPR: &str =
    "('{CUSTOMER}', '{UPLOAD_TYPE}', '{TEST_AREA}', '{FACTORY}', '{SUB_CUSTOMER}', '{DEVICE_ID}', {LOT_BUCKET})";

#[derive(Debug, <PERSON>lone)]
pub struct TestItemDetailHandler {
    pub db_name: String,
    pub table_name: String,
}

impl TestItemDetailHandler {
    pub fn new(db_name: String, insert_cluster_table: bool) -> Self {
        let table_name = if insert_cluster_table {
            TABLE_NAME.replace(CLUSTER_NAME, CLUSTER_TABLE)
        } else {
            TABLE_NAME.replace(CLUSTER_NAME, LOCAL_TABLE)
        };
        Self { db_name, table_name }
    }
}

// 这部分是核心改进：为 TestItemDetailHandler 实现 SinkHandler<T>
impl SinkHandler<TestItemDetailRow> for TestItemDetailHandler {
    fn db_name(&self) -> &str {
        &self.db_name
    }

    fn table_name(&self) -> &str {
        &self.table_name
    }
    fn partition_expr(&self) -> &str {
        PARTITION_EXPR
    }
}
