use env_logger;
use parquet_provider::hdfs_provider::HdfsProvider;

#[tokio::main]
async fn main() {
    let hdfs_provider = HdfsProvider::new_default().unwrap();
    let files = hdfs_provider.list_directory("/user/glory/cz", false).await.unwrap();
    println!("end {:?}", files);
    let down = hdfs_provider.download_file("/user/glory/data/onedata/dataware/ods/result/STDF/TEST_ITEM_DATA/TEST_AREA=FT/CUSTOMER=GUWAVE/FACTORY=CZ_343_002/DEVICE_ID=cz343002/LOT_ID=gubocz343002/TEST_STAGE=FT1/LOT_TYPE=PRODUCTION/FILE_ID=667243/part-3_gubo_demo_2025-07-22_13_50_03.stdf.test_item_data.parquet",
                                           "C://1_D//adapter//tmp.parquet").await;
    match down {
        Ok(_) => println!("下载成功"),
        Err(e) => eprintln!("下载失败: {:?}", e),
    }
    let upload = hdfs_provider
        .upload_file(
            "C://1_D//adapter//parquet//part-3_gubo_demo_2025-07-22_13_50_03.stdf.test_item_data.parquet",
            "/user/glory/tmp/tmp0725/a.parquet",
            true,
        )
        .await.unwrap();
    let res = hdfs_provider.copy(
        "C://1_D//adapter//parquet//part-3_gubo_demo_2025-07-22_13_50_03.stdf.test_item_data.parquet",
        "C://1_D//adapter//tmp.parquet",
    ).await.unwrap();
    
}
