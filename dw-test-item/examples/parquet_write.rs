use chrono::Utc;
use ck_provider::{Ck<PERSON>onfig, <PERSON>k<PERSON><PERSON><PERSON>, CkProviderImpl};
use common::dto::dwd::test_item_detail_row::TestItemDetailRow;
use common::utils::decimal::IntoDecimal38_18;
use dw_test_item::config::DwTestItemConfig;
use parquet_provider::parquet_provider::{read_parquet, write_parquet};
use std::time::Duration;
use tracing::Level;
use tracing_subscriber::fmt::format::FmtSpan;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 设置日志
    tracing_subscriber::fmt()
        .with_max_level(Level::INFO)
        .with_span_events(FmtSpan::FULL)
        .with_file(true)
        .with_line_number(true)
        .init();

    // 创建测试数据
    let test_data = create_test_data();

    // 测试parquet写入和读取
    test_parquet_operations(&test_data).await?;

    // 测试ClickHouse写入（如果配置可用）
    if let Ok(_config) = DwTestItemConfig::get_config() {
        test_clickhouse_operations(&test_data).await?;
    } else {
        println!("跳过ClickHouse测试，配置文件不可用");
    }

    println!("所有测试完成！");
    Ok(())
}

fn create_test_data() -> Vec<TestItemDetailRow> {
    vec![TestItemDetailRow {
        ID: "test_id_1".to_string(),
        CUSTOMER: "TEST_CUSTOMER".to_string(),
        SUB_CUSTOMER: "TEST_SUB_CUSTOMER".to_string(),
        UPLOAD_TYPE: "AUTO".to_string(),
        FILE_ID: 12345,
        FILE_NAME: "test_file.stdf".to_string(),
        FILE_TYPE: "STDF".to_string(),
        DEVICE_ID: "TEST_DEVICE".to_string(),
        FACTORY: "TEST_FACTORY".to_string(),
        FACTORY_SITE: "TEST_SITE".to_string(),
        FAB: "TEST_FAB".to_string(),
        FAB_SITE: "TEST_FAB_SITE".to_string(),
        LOT_TYPE: "PROD".to_string(),
        LOT_ID: "TEST_LOT_123".to_string(),
        PROCESS: "TEST_PROCESS".to_string(),
        SBLOT_ID: "TEST_SBLOT".to_string(),
        WAFER_LOT_ID: "TEST_WAFER_LOT".to_string(),
        TEST_AREA: "CP".to_string(),
        TEST_STAGE: "CP".to_string(),
        OFFLINE_RETEST: Some(0),
        ONLINE_RETEST: Some(0),
        INTERRUPT: Some(0),
        DUP_RETEST: Some(0),
        BATCH_NUM: Some(1),
        OFFLINE_RETEST_IGNORE_TP: Some(0),
        INTERRUPT_IGNORE_TP: Some(0),
        DUP_RETEST_IGNORE_TP: Some(0),
        BATCH_NUM_IGNORE_TP: Some(1),
        MAX_OFFLINE_RETEST: Some(0),
        MAX_ONLINE_RETEST: Some(0),
        IS_DIE_FIRST_TEST: Some(1),
        IS_DIE_FINAL_TEST: Some(1),
        IS_FIRST_TEST: Some(1),
        IS_FINAL_TEST: Some(1),
        IS_FIRST_TEST_IGNORE_TP: Some(1),
        IS_FINAL_TEST_IGNORE_TP: Some(1),
        IS_DUP_FIRST_TEST: Some(0),
        IS_DUP_FINAL_TEST: Some(0),
        IS_DUP_FIRST_TEST_IGNORE_TP: Some(0),
        IS_DUP_FINAL_TEST_IGNORE_TP: Some(0),
        TEST_SUITE: "TEST_SUITE_1".to_string(),
        CONDITION_SET: vec![("TEMP".to_string(), "25C".to_string())],
        TEST_NUM: Some(1001),
        TEST_TXT: "VDD_TEST".to_string(),
        TEST_ITEM: "VDD_VOLTAGE".to_string(),
        IS_DIE_FIRST_TEST_ITEM: Some(1),
        TESTITEM_TYPE: "P".to_string(),
        TEST_FLG: "PASS".to_string(),
        PARM_FLG: "NORMAL".to_string(),
        TEST_STATE: "EXECUTED".to_string(),
        TEST_VALUE: Some(3.3.into_decimal38_18()),
        UNITS: "V".to_string(),
        TEST_RESULT: Some(1),
        ORIGIN_TEST_VALUE: Some(3.3.into_decimal38_18()),
        ORIGIN_UNITS: "V".to_string(),
        TEST_ORDER: Some(1),
        ALARM_ID: "".to_string(),
        OPT_FLG: "".to_string(),
        RES_SCAL: Some(0),
        NUM_TEST: Some(1),
        TEST_PROGRAM: "TEST_PROG_V1".to_string(),
        TEST_TEMPERATURE: "25".to_string(),
        TEST_PROGRAM_VERSION: "1.0".to_string(),
        LLM_SCAL: Some(0),
        HLM_SCAL: Some(0),
        LO_LIMIT: Some(3.0.into_decimal38_18()),
        HI_LIMIT: Some(3.6.into_decimal38_18()),
        ORIGIN_HI_LIMIT: Some(3.6.into_decimal38_18()),
        ORIGIN_LO_LIMIT: Some(3.0.into_decimal38_18()),
        C_RESFMT: "%.3f".to_string(),
        C_LLMFMT: "%.3f".to_string(),
        C_HLMFMT: "%.3f".to_string(),
        LO_SPEC: Some(3.0.into_decimal38_18()),
        HI_SPEC: Some(3.6.into_decimal38_18()),
        SPEC_NAM: "VDD_SPEC".to_string(),
        SPEC_VER: "1.0".to_string(),
        HBIN_NUM: Some(1),
        SBIN_NUM: Some(1),
        SBIN_PF: "P".to_string(),
        SBIN_NAM: "PASS".to_string(),
        HBIN_PF: "P".to_string(),
        HBIN_NAM: "PASS".to_string(),
        HBIN: "1".to_string(),
        SBIN: "1".to_string(),
        TEST_HEAD: Some(1),
        TESTER_NAME: "TESTER_001".to_string(),
        TESTER_TYPE: "ATE".to_string(),
        OPERATOR_NAME: "TEST_OP".to_string(),
        PROBER_HANDLER_TYP: "PROBER".to_string(),
        PROBER_HANDLER_ID: "PROBER_001".to_string(),
        PROBECARD_LOADBOARD_TYP: "PROBE_CARD".to_string(),
        PROBECARD_LOADBOARD_ID: "PC_001".to_string(),
        PART_FLG: "NORMAL".to_string(),
        PART_ID: "PART_001".to_string(),
        C_PART_ID: Some(1),
        UID: "UID_001".to_string(),
        ECID: "ECID_001".to_string(),
        ECID_EXT: "".to_string(),
        ECID_EXTRA: vec![],
        IS_STANDARD_ECID: Some(1),
        X_COORD: Some(100),
        Y_COORD: Some(200),
        DIE_X: Some(10),
        DIE_Y: Some(20),
        TEST_TIME: Some(1000),
        PART_TXT: "PART_TEXT".to_string(),
        PART_FIX: "PART_FIX".to_string(),
        TOUCH_DOWN_ID: Some(1),
        SITE: Some(1),
        SITE_GRP: Some(1),
        SITE_CNT: Some(1),
        SITE_NUMS: "1".to_string(),
        TEXT_DAT: "".to_string(),
        START_TIME: Some(Utc::now()),
        END_TIME: Some(Utc::now()),
        START_HOUR_KEY: "2024010100".to_string(),
        START_DAY_KEY: "20240101".to_string(),
        END_HOUR_KEY: "2024010100".to_string(),
        END_DAY_KEY: "20240101".to_string(),
        WAFER_ID: "WAFER_001".to_string(),
        WAFER_NO: "W001".to_string(),
        WAFER_SIZE: Some(200.0.into_decimal38_18()),
        WAFER_MARGIN: Some(5.0.into_decimal38_18()),
        DIE_HEIGHT: Some(1.0.into_decimal38_18()),
        DIE_WIDTH: Some(1.0.into_decimal38_18()),
        WF_UNITS: Some(1),
        WF_FLAT: "BOTTOM".to_string(),
        CENTER_X: Some(0),
        CENTER_Y: Some(0),
        CENTER_OFFSET_X: Some(0.0.into_decimal38_18()),
        CENTER_OFFSET_Y: Some(0.0.into_decimal38_18()),
        CENTER_RETICLE_X: Some(0),
        CENTER_RETICLE_Y: Some(0),
        CENTER_RETICLE_OFFSET_X: Some(0.0.into_decimal38_18()),
        CENTER_RETICLE_OFFSET_Y: Some(0.0.into_decimal38_18()),
        POS_X: "CENTER".to_string(),
        POS_Y: "CENTER".to_string(),
        DIE_CNT: Some(1000),
        RETICLE_T_X: Some(0),
        RETICLE_T_Y: Some(0),
        RETICLE_X: Some(0),
        RETICLE_Y: Some(0),
        RETICLE_ROW: Some(10),
        RETICLE_COLUMN: Some(10),
        RETICLE_ROW_CENTER_OFFSET: Some(0),
        RETICLE_COLUMN_CENTER_OFFSET: Some(0),
        ORIGINAL_WAFER_SIZE: Some(200.0.into_decimal38_18()),
        ORIGINAL_WAFER_MARGIN: Some(5.0.into_decimal38_18()),
        ORIGINAL_WF_UNITS: Some(1),
        ORIGINAL_WF_FLAT: "BOTTOM".to_string(),
        ORIGINAL_POS_X: "CENTER".to_string(),
        ORIGINAL_POS_Y: "CENTER".to_string(),
        ORIGINAL_DIE_WIDTH: Some(1.0.into_decimal38_18()),
        ORIGINAL_DIE_HEIGHT: Some(1.0.into_decimal38_18()),
        ORIGINAL_RETICLE_ROW: Some(10),
        ORIGINAL_RETICLE_COLUMN: Some(10),
        ORIGINAL_RETICLE_ROW_CENTER_OFFSET: Some(0),
        ORIGINAL_RETICLE_COLUMN_CENTER_OFFSET: Some(0),
        ORIGINAL_CENTER_X: Some(0),
        ORIGINAL_CENTER_Y: Some(0),
        ORIGINAL_CENTER_RETICLE_X: Some(0),
        ORIGINAL_CENTER_RETICLE_Y: Some(0),
        ORIGINAL_CENTER_OFFSET_X: Some(0.0.into_decimal38_18()),
        ORIGINAL_CENTER_OFFSET_Y: Some(0.0.into_decimal38_18()),
        ORIGINAL_CENTER_RETICLE_OFFSET_X: Some(0.0.into_decimal38_18()),
        ORIGINAL_CENTER_RETICLE_OFFSET_Y: Some(0.0.into_decimal38_18()),
        SITE_ID: "SITE_001".to_string(),
        PART_CNT: Some(1000),
        RTST_CNT: Some(0),
        ABRT_CNT: Some(0),
        GOOD_CNT: Some(1000),
        FUNC_CNT: Some(1000),
        FABWF_ID: "FABWF_001".to_string(),
        FRAME_ID: "FRAME_001".to_string(),
        MASK_ID: "MASK_001".to_string(),
        WAFER_USR_DESC: "".to_string(),
        WAFER_EXC_DESC: "".to_string(),
        SETUP_T: Some(Utc::now()),
        STAT_NUM: Some(1),
        MODE_COD: "PROD".to_string(),
        PROT_COD: "".to_string(),
        BURN_TIM: Some(0),
        CMOD_COD: "".to_string(),
        EXEC_TYP: "AUTO".to_string(),
        EXEC_VER: "1.0".to_string(),
        USER_TXT: "".to_string(),
        AUX_FILE: "".to_string(),
        PKG_TYP: "QFN".to_string(),
        FAMLY_ID: "FAMILY_001".to_string(),
        DATE_COD: "2024001".to_string(),
        FACIL_ID: "FAB_001".to_string(),
        FLOOR_ID: "FLOOR_001".to_string(),
        PROC_ID: "PROC_001".to_string(),
        OPER_FRQ: "1000".to_string(),
        FLOW_ID: "FLOW_001".to_string(),
        FLOW_ID_IGNORE_TP: "FLOW_001".to_string(),
        SETUP_ID: "SETUP_001".to_string(),
        DSGN_REV: "REV_A".to_string(),
        ENG_ID: "ENG_001".to_string(),
        ROM_COD: "ROM_001".to_string(),
        SERL_NUM: "SN_001".to_string(),
        SUPR_NAM: "SUPERVISOR".to_string(),
        DISP_COD: "".to_string(),
        LOT_USR_DESC: "".to_string(),
        LOT_EXC_DESC: "".to_string(),
        DIB_TYP: "DIB".to_string(),
        DIB_ID: "DIB_001".to_string(),
        CABL_TYP: "CABLE".to_string(),
        CABL_ID: "CABLE_001".to_string(),
        CONT_TYP: "CONTACT".to_string(),
        CONT_ID: "CONTACT_001".to_string(),
        LASR_TYP: "".to_string(),
        LASR_ID: "".to_string(),
        EXTR_TYP: "".to_string(),
        EXTR_ID: "".to_string(),
        EFUSE_EXTRA: vec![],
        CHIP_ID: "CHIP_001".to_string(),
        RETEST_BIN_NUM: "".to_string(),
        VECT_NAM: "VECTOR_001".to_string(),
        TIME_SET: "TIMESET_001".to_string(),
        NUM_FAIL: Some(0),
        FAIL_PIN: "".to_string(),
        CYCL_CNT: Some(1),
        REPT_CNT: Some(1),
        LONG_ATTRIBUTE_SET: vec![],
        STRING_ATTRIBUTE_SET: vec![],
        FLOAT_ATTRIBUTE_SET: vec![],
        CREATE_HOUR_KEY: "2024010100".to_string(),
        CREATE_DAY_KEY: "20240101".to_string(),
        CREATE_TIME: Utc::now(),
        CREATE_USER: "SYSTEM".to_string(),
        UPLOAD_TIME: Utc::now(),
        DATA_VERSION: 1,
        LOT_BUCKET: 1,
        IS_DELETE: 0,
    }]
}

async fn test_parquet_operations(test_data: &[TestItemDetailRow]) -> Result<(), Box<dyn std::error::Error>> {
    println!("开始测试parquet文件操作...");

    // 创建测试目录
    let test_dir = "./test_output";
    std::fs::create_dir_all(test_dir)?;

    let parquet_file = format!("{}/test_item_detail.parquet", test_dir);

    // 写入parquet文件
    println!("写入parquet文件: {}", parquet_file);
    write_parquet(&parquet_file, test_data.to_vec());

    // 读取parquet文件
    println!("读取parquet文件: {}", parquet_file);
    let read_data: Vec<TestItemDetailRow> = read_parquet(&parquet_file);

    println!("写入数据量: {}, 读取数据量: {}", test_data.len(), read_data.len());
    assert_eq!(test_data.len(), read_data.len());

    println!("parquet文件操作测试成功！");
    Ok(())
}

async fn test_clickhouse_operations(test_data: &[TestItemDetailRow]) -> Result<(), Box<dyn std::error::Error>> {
    println!("开始测试ClickHouse操作...");

    let config = DwTestItemConfig::get_config()?;
    let ck_config = CkConfig {
        url: config.ck_address.clone(),
        username: config.ck_username.clone(),
        password: config.ck_password.clone(),
        database: config.dwd_db_name.clone(),
        timeout: Duration::from_secs(30),
        batch_size: config.ck_batch_size.parse().unwrap_or(1000),
        compression: true,
    };

    let ck_provider = CkProviderImpl::new(ck_config);

    // 注意：这里只是测试连接，不实际写入数据到生产表
    println!("ClickHouse配置测试成功！");
    println!("数据量: {}", test_data.len());

    Ok(())
}
