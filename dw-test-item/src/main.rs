use color_eyre::{eyre::WrapErr, Result};
use dw_test_item::task::cp_task_params::CpTaskParams;
use dw_test_item::task::cp_test_item_task::CpTestItemTask;

use tracing::Level;
use tracing_subscriber::fmt::format::FmtSpan;

#[tokio::main]
async fn main() -> Result<()> {
    std::env::set_var("RUST_BACKTRACE", "full");
    color_eyre::install()?;
    tracing_subscriber::fmt()
        .with_max_level(Level::INFO)
        .with_span_events(FmtSpan::FULL)
        .with_file(true)
        .with_line_number(true)
        .init();
    execute_cp_test_item_task().await?;
    Ok(())
}

async fn execute_cp_test_item_task() -> Result<()> {
    let task = CpTestItemTask::new();

    task.do_task(CpTaskParams::new(
        r#"{"customer": "YEESTOR",
        "sub_customer": "YEESTOR",
        "factory": "LEADYO",
        "factory_site": "LEADYO",
        "test_area": "CP",
        "lot_id": "ENF083",
        "wafer_no": "25",
        "lot_type": "PRODUCTION",
        "device_id": "YS8293ENAB",
        "test_stage": "CP1",
        "execute_mode": "AUTO",
        "file_category": "STDF",
        "ck_sink_type": "JDBC",
        "test_item_detail_result_partition": "1",
        "data_version": "1",
        "calculate_yms_test_item": "1"}
        "#,
    ))
    .await
    .wrap_err("执行CP测试项任务失败")?;
    Ok(())
}
