use serde::Deserialize;

/// Task execution parameters corresponding to the JSON parameters in Scala main method
#[derive(Debug, Clone, Deserialize)]
pub struct CpTaskParams {
    pub customer: String,
    pub sub_customer: String,
    pub factory: String,
    pub factory_site: String,
    pub test_area: String,
    pub lot_id: String,
    pub wafer_no: String,
    pub lot_type: String,
    pub device_id: String,
    pub test_stage: String,
    pub execute_mode: String,
    pub file_category: String,
    pub ck_sink_type: String,
    pub test_item_detail_result_partition: String,
    pub data_version: String,
    pub calculate_yms_test_item: String,
}

impl CpTaskParams {
    pub fn new(params: &str) -> Self {
        serde_json::from_str(params).unwrap()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_new() {
        let params = CpTaskParams::new(
            r#"{"customer": "YEESTOR", 
            "sub_customer": "YEESTOR", 
            "factory": "LEADYO", 
            "factory_site": "LEADYO", 
            "test_area": "CP", 
            "lot_id": "ENF083", 
            "wafer_no": "25", 
            "lot_type": "PRODUCTION", 
            "device_id": "YS8293ENAB", 
            "test_stage": "CP1", 
            "execute_mode": "AUTO", 
            "file_category": "STDF", 
            "ck_sink_type": "JDBC", 
            "test_item_detail_result_partition": "1", 
            "data_version": "0", 
            "calculate_yms_test_item": "1"}
            "#,
        );
        assert_eq!(params.customer, "YEESTOR");
        assert_eq!(params.sub_customer, "YEESTOR");
        assert_eq!(params.factory, "LEADYO");
        assert_eq!(params.factory_site, "LEADYO");
        assert_eq!(params.test_area, "CP");
        assert_eq!(params.lot_id, "ENF083");
        assert_eq!(params.wafer_no, "25");
        assert_eq!(params.lot_type, "PRODUCTION");
        assert_eq!(params.device_id, "YS8293ENAB");
        assert_eq!(params.test_stage, "CP1");
        assert_eq!(params.execute_mode, "AUTO");
        assert_eq!(params.file_category, "STDF");
        assert_eq!(params.ck_sink_type, "JDBC");
        assert_eq!(params.test_item_detail_result_partition, "1");
        assert_eq!(params.data_version, "0");
        assert_eq!(params.calculate_yms_test_item, "1");
    }
}
