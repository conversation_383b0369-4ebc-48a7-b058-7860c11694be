use bumpalo::Bump;
use color_eyre::{eyre::WrapErr, Result};
use std::time::Instant;

use crate::config::DwTestItemConfig;
use crate::service::cp_dwd_test_item_service::CpDwdTestItemService;
use crate::task::cp_task_params::CpTaskParams;
use common::ck::ck_operate::CkOperate;
use common::dto::dwd::die_detail_parquet::DieDetailParquet;
use common::dto::ods::test_item_data_parquet::TestItemDataParquet;
use common::dwd::sink::test_item_detail_handler::TestItemDetailHandler;
use common::dwd::sink::SinkHandler;
use common::dwd::util::dwd_common_util::DwdCommonUtil;
use common::model::constant;
use common::model::constant::run_mode::RunMode;
use common::model::key::wafer_key::<PERSON><PERSON><PERSON><PERSON><PERSON>;
use common::utils::path;
use parquet_provider::parquet_provider::read_parquet;

// Corresponding to Scala file:
// /dataware/dataware-dw/dataware-dw-test-item/src/main/scala/com/guwave/onedata/dataware/dw/testItem/spark/task/impl/CpTestItemTask.scala

/// CpTestItemTask orchestrates the complete CP test item data processing pipeline
/// This is the main entry point for CP stage data warehouse processing
#[derive(Debug)]
pub struct CpTestItemTask {
    config: DwTestItemConfig,
}

impl CpTestItemTask {
    /// Create new CpTestItemTask with configuration
    pub fn new() -> Self {
        let config = DwTestItemConfig::get_config().unwrap();
        Self { config }
    }

    /// Execute the complete CP test item task
    ///
    /// Corresponds to: CpTestItemTask.scala:29-105 (doTask method)
    /// private def doTask(customer: String, subCustomer: String, factory: String, factorySite: String,
    ///                   testArea: String, lotId: String, waferNo: String, lotType: String, deviceId: String,
    ///                   testStage: String, mode: String, fileCategory: String, ckSinkType: String,
    ///                   testItemDetailResultPartition: String, dataVersion: String, calculateYmsTestItem: String): Unit
    pub async fn do_task(&self, params: CpTaskParams) -> Result<()> {
        let start_time = Instant::now();
        log::info!("cp test item task start...");
        let bump = Bump::new();

        self.execute_tombstone_operations(
            &params.customer,
            &params.factory,
            &params.test_area,
            &params.lot_id,
            &params.lot_type,
            &params.test_stage,
            &params.device_id,
            &params.wafer_no,
        )
        .await
        .wrap_err("执行tombstone操作失败")?;

        let die_detail_path = path::get_dwd_wafer_path(
            &self.config.cp_die_detail_result_dir,
            &params.test_area,
            &params.customer,
            &params.factory,
            &params.lot_id,
            &params.wafer_no,
            &params.device_id,
            &params.test_stage,
            &params.lot_type,
        );
        let base_path = if self.config.cp_result_dir.ends_with(constant::SLASH) {
            self.config.cp_result_dir.clone()
        } else {
            format!("{}{}", self.config.cp_result_dir, constant::SLASH)
        };
        let test_item_data_path = path::get_ods_wafer_path(
            &base_path,
            &params.file_category,
            &params.test_area,
            &params.customer,
            &params.factory,
            constant::TEST_ITEM_DATA,
            &params.lot_id,
            &params.wafer_no,
            &params.device_id,
            &params.test_stage,
            &params.lot_type,
        );
        log::info!("当前Task需要计算的数据集: [DieDetail={}], [TestItemData={}]", die_detail_path, test_item_data_path);
        let die_detail = self.load_die_detail_data(&die_detail_path).await?;
        let test_item_data = self.load_test_item_data(&test_item_data_path).await?;

        // 计算dwd test item
        let wafer_key = self.create_wafer_key(&params);
        let _file_map_and_die_and_test_item_detail = bump.alloc(
            CpDwdTestItemService::new(self.config.test_item_detail_result_partition, params.test_area.clone())
                .calculate(
                    die_detail,
                    test_item_data,
                    &wafer_key,
                    params.test_area,
                    params.execute_mode,
                    params.file_category,
                    params.ck_sink_type,
                    RunMode::RUST.to_string(),
                )
                .await
                .map_err(|e| {
                    let err_str = e.to_string();
                    log::error!("计算dwd test item失败: {}", e);
                    crate::error::DatawareError::DwdCalculateFailed(err_str)
                })
                .wrap_err("计算dwd test item失败")?,
        );

        drop(bump);
        let duration = start_time.elapsed();
        log::info!("cp test item task end...任务耗时：[{:?}]", duration);
        Ok(())
    }

    /// Execute tombstone operations for ClickHouse cleanup
    ///
    /// Corresponds to: CpTestItemTask.scala:49-57
    async fn execute_tombstone_operations(
        &self,
        customer: &str,
        factory: &str,
        test_area: &str,
        lot_id: &str,
        lot_type: &str,
        test_stage: &str,
        device_id: &str,
        wafer_no: &str,
    ) -> Result<()> {
        // 1. Create TestItemDetailHandler (lines 50-51 in Scala)
        //    new TestItemDetailHandler(properties.getDwdDbName, properties.getInsertClusterTable)
        let handler = TestItemDetailHandler::new(self.config.dwd_db_name.clone(), self.config.insert_cluster_table);

        // 2. Calculate lot bucket (line 54 in Scala)
        //    DwdCommonUtil.getLotBucket(lotId, properties.getLotBucketNum)
        let lot_bucket = DwdCommonUtil::get_lot_bucket(lot_id, self.config.lot_bucket_num);

        // 3. Build table full name (line 55 in Scala)
        //    handler.dbName + "." + handler.tableName
        let table_full_name = format!("{}.{}", handler.db_name(), handler.table_name());

        // 4. Call CkOperate.tombstoneCk (lines 53-56 in Scala)
        CkOperate::tombstone_ck(
            customer,
            factory,
            test_area,
            lot_id,
            lot_type,
            test_stage,
            device_id,
            Some(lot_bucket),
            wafer_no,
            &table_full_name,
            &self.config.get_ck_address(),
            &self.config.get_ck_address_list(),
            &self.config.ck_username,
            &self.config.ck_password,
            handler.partition_expr(),
            Some(chrono::Utc::now()),
        )
        .await
        .map_err(|e| {
            let err_str = e.to_string();
            log::error!("执行tombstone操作失败: {}", e);
            crate::error::DatawareError::TombstoneFailed(err_str)
        })?;

        Ok(())
    }

    /// Load die detail data from parquet files
    ///
    /// Corresponds to: CpTestItemTask.scala:65 (die detail loading)
    async fn load_die_detail_data(&self, die_detail_path: &str) -> Result<Vec<DieDetailParquet>> {
        // TODO: read from HDFS
        let die_detail = read_parquet::<DieDetailParquet>(die_detail_path);
        Ok(die_detail)
    }

    /// Load test item data from parquet files
    ///
    /// Corresponds to: CpTestItemTask.scala:66 (test item data loading)
    async fn load_test_item_data(&self, test_item_data_path: &str) -> Result<Vec<TestItemDataParquet>> {
        // TODO: read from HDFS
        let test_item_data = read_parquet::<TestItemDataParquet>(test_item_data_path);
        Ok(test_item_data)
    }

    /// Create wafer key from task parameters
    fn create_wafer_key(&self, params: &CpTaskParams) -> WaferKey {
        WaferKey {
            customer: params.customer.clone(),
            sub_customer: params.sub_customer.clone(),
            factory: params.factory.clone(),
            factory_site: params.factory_site.clone(),
            test_area: params.test_area.clone(),
            test_stage: params.test_stage.clone(),
            device_id: params.device_id.clone(),
            lot_type: params.lot_type.clone(),
            lot_id: params.lot_id.clone(),
            wafer_no: params.wafer_no.clone(),
        }
    }
}
