[package]
name = "ck-provider"
version = "0.1.0"
edition = "2021"

[dependencies]
clickhouse = { version = "0.13.3", features = ["lz4", "uuid", "time", "chrono"] }
tokio = { workspace = true }
anyhow = { workspace = true }
async-trait = { workspace = true }
chrono = { workspace = true }
thiserror = { workspace = true }
futures = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
uuid = { version = "1.0", features = ["serde", "v4"] }
time = "0.3"
log = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }

[features]
run-tests = []

[dev-dependencies]
tokio = { workspace = true }

[[example]]
name = "basic_usage"
path = "examples/basic_usage.rs"